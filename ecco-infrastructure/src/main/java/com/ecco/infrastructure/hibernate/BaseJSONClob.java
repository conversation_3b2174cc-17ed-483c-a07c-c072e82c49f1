package com.ecco.infrastructure.hibernate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.hibernate.HibernateException;

import java.io.BufferedReader;
import java.io.IOException;
import java.sql.Clob;
import java.sql.ResultSet;
import java.sql.SQLException;

public abstract class BaseJSONClob<T> implements J<PERSON>NClob<T> {

    private final ObjectMapper MAPPER;

    public BaseJSONClob(ObjectMapper mapper) {
        this.MAPPER = mapper;
    }

    @SneakyThrows
    public T getFromClob(ResultSet rs, String name) throws SQLException {
        Clob clob = rs.getClob(name);
        if (rs.wasNull() || clob == null || clob.length() == 0) {
            return null;
        }
        try {
            return getMapper().readValue(clob.getCharacterStream(), getTargetType());
        } catch (IOException e) {
            var buffer = new BufferedReader(clob.getCharacterStream());
            StringBuilder sb = new StringBuilder();
            String line;
            while(null != (line = buffer.readLine())) {
                sb.append(line);
            }
            throw new HibernateException("unable to read object from result set with: " + sb, e);
        }
    }

    @Override
    public String writeValueAsString(Object value) throws JsonProcessingException {
        return getMapper().writeValueAsString(value);
    }

    @Override
    public ObjectMapper getMapper() {
        return this.MAPPER;
    }
}
