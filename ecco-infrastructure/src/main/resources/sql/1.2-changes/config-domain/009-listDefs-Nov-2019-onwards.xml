<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd"
        logicalFilePath="classpath:sql/1.2-changes/config-domain/009-listDefs-Nov-2019-onwards.xml">

    <changeSet id="DEV-1200-appt-type-skills-listDef" author="adamjhamer">
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="152"/>
            <column name="listName" value="apptTypeRequirements"/>
            <column name="name" value="cooking"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="153"/>
            <column name="listName" value="apptTypeRequirements"/>
            <column name="name" value="lifting"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="154"/>
            <column name="listName" value="apptTypeRequirements"/>
            <column name="name" value="medication"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="155"/>
            <column name="listName" value="apptTypeRequirements"/>
            <column name="name" value="personal hygene"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="156"/>
            <column name="listName" value="apptTypeRequirements"/>
            <column name="name" value="female-only"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="157"/>
            <column name="listName" value="apptTypeRequirements"/>
            <column name="name" value="male-only"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1275-building-resource-listDef" author="adamjhamer">
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="158"/>
            <column name="listName" value="resourceType"/>
            <column name="name" value="care run"/>
            <column name="parentId" valueNumeric="132"/>
            <column name="metadata">{}</column>
        </insert>
    </changeSet>
    <changeSet id="DEV-1358-initial-eventStatusRateId-list" author="nealeu">
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="159"/>
            <column name="listName" value="eventStatusRateId"/>
            <column name="name" value="cancelled (no charge)"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="160"/>
            <column name="listName" value="eventStatusRateId"/>
            <column name="name" value="waking nights shift"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1401-ext-system-demo-external-results" author="nealeu">
        <update tableName="cfg_externalsystem">
            <column name="name" value="external results" />
            <where>name='demo'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-283-relationships-to-listdef" author="adamjhamer">
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="161"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="father"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="162"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="mother"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="163"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="grandmother"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="164"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="grandfather"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="165"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="step father"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="166"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="step mother"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="167"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="guardian"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="168"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="partner"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="169"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="foster mother"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="170"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="foster father"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="171"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="aunt"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="172"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="uncle"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="173"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="brother"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="174"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="sister"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="175"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="step brother"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="176"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="step sister"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="177"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="half brother"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="178"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="half sister"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="179"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="cousin"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="180"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="carer"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="181"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="son"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="182"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="daughter"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="183"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="step son"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="184"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="step daughter"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="185"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="foster brother"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="186"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="foster sister"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="187"/>
            <column name="listName" value="relationshipAssociationType"/> <!-- similar to associatedTypeId, which is suggested as contactAssociationType -->
            <column name="name" value="ex-partner"/>
        </insert>
        <update tableName="cfg_list_definitions">
            <column name="metadata" value="{}"/>
            <where>metadata is null</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-1607-listdef-businesskey" author="adamjhamer">
        <addColumn tableName="cfg_list_definitions">
            <column name="businessKey" type="VARCHAR(128)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <update tableName="cfg_list_definitions">
            <!-- everything gets the id here -->
            <column name="businessKey" valueComputed="id"/>
        </update>
        <addNotNullConstraint tableName="cfg_list_definitions" columnName="businessKey" columnDataType="VARCHAR(128)"/>
        <addUniqueConstraint tableName="cfg_list_definitions" columnNames="businessKey"/>
    </changeSet>

    <!-- *** STOP: DO NOT ADD ANYTHING MORE HERE - USE A CHANGELOG in the correct YEAR folder *** -->

</databaseChangeLog>
