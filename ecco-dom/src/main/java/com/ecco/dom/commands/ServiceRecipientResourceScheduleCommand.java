package com.ecco.dom.commands;

import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import lombok.NoArgsConstructor;
import org.joda.time.Instant;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;


@Entity
@DiscriminatorValue("editResourceSchedule")
@NoArgsConstructor
public class ServiceRecipientResourceScheduleCommand extends ServiceRecipientCommand {


    public ServiceRecipientResourceScheduleCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                                                   long userId, @NonNull String body, int serviceRecipientId) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId);
    }

}
