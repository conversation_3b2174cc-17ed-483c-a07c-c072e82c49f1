package com.ecco.acceptancetests.ui.pages.referral;

import java.util.Date;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;

public class StartPage extends EccoBasePage {

    private static final String URL = "/dynamic/secure/referralAspectFlow";

    //private static final String CHCKBOX_ANY_PROJECT = "anyProject";
    private static final String DROPDOWN_STAFF_NAME = "supportWorker";

    ReferralViewPage referralPage;
    public StartPage(ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
    }

    public void setDetails(String supportWorker, Date startDate, boolean started) {
        setSelection(DROPDOWN_STAFF_NAME, supportWorker);
        setDateByElementId("receivingServiceDate", startDate);

        if (started) {
            WebElement checkbox = getWebDriver().findElement(By.name("receivingService"));
            if (!checkbox.isSelected()) {
                clickElementWithRetry(checkbox);
            }
        }
        clickButtonByText(EccoBasePage.BUTTON_SAVE);
    }

    @Override
    public EccoBasePage defaultAction() {
        setDetails("sysadmin", new Date(), true);
        return referralPage;
    }

}
