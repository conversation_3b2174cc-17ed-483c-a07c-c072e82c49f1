package com.ecco.web.filter;

import com.ecco.security.SecurityUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * A servlet filter to log basic information about HTTP requests.
 *
 * Format string syntax:
 *
 * <table cellspacing="2" cellpadding="4"border="0"><tr><th bgcolor="#CCCCCC" class="twikiFirstCol"> <a rel="nofollow" href="http://wiki.osafoundation.org/bin/view/Journal/HttpLoggingFilterDoc?sortcol=0;table=1;up=0#sorted_table" title="Sort by this column"><font color="#000000">Request parameter</font></a> </th><th bgcolor="#CCCCCC"> <a rel="nofollow" href="http://wiki.osafoundation.org/bin/view/Journal/HttpLoggingFilterDoc?sortcol=1;table=1;up=0#sorted_table" title="Sort by this column"><font color="#000000">Formatting String</font></a> </th><th bgcolor="#CCCCCC"> <a rel="nofollow" href="http://wiki.osafoundation.org/bin/view/Journal/HttpLoggingFilterDoc?sortcol=2;table=1;up=0#sorted_table" title="Sort by this column"><font color="#000000">Request Object Access Method</font></a> </th></tr>
 * <tr><td bgcolor="#EEEEEE" align="left"> Method </td><td bgcolor="#EEEEEE" align="left"> %M </td><td bgcolor="#EEEEEE" align="left"> getMethod() </td></tr>
 *
 * <tr><td bgcolor="#EEEEEE" align="left"> Scheme </td><td bgcolor="#EEEEEE" align="left"> %C </td><td bgcolor="#EEEEEE" align="left"> getScheme() </td></tr>
 * <tr><td bgcolor="#EEEEEE" align="left"> Server </td><td bgcolor="#EEEEEE" align="left"> %S </td><td bgcolor="#EEEEEE" align="left"> getServerName() </td></tr>
 * <tr><td bgcolor="#EEEEEE" align="left"> Port </td><td bgcolor="#EEEEEE" align="left"> %P </td><td bgcolor="#EEEEEE" align="left"> getServerPort() </td></tr>
 *
 * <tr><td bgcolor="#EEEEEE" align="left"> Request URI </td><td bgcolor="#EEEEEE" align="left"> %U </td><td bgcolor="#EEEEEE" align="left"> getRequestURI() </td></tr>
 * <tr><td bgcolor="#EEEEEE" align="left"> Content Length </td><td bgcolor="#EEEEEE" align="left"> %L </td><td bgcolor="#EEEEEE" align="left"> getContentLength() </td></tr>
 * <tr><td bgcolor="#EEEEEE" align="left"> Query String </td><td bgcolor="#EEEEEE" align="left"> %Q </td><td bgcolor="#EEEEEE" align="left"> getQueryString() </td></tr>
 *
 * <tr><td bgcolor="#EEEEEE" align="left"> Session Id </td><td bgcolor="#EEEEEE" align="left"> %I </td><td bgcolor="#EEEEEE" align="left"> getSession().getId() </td></tr>
 * <tr><td bgcolor="#EEEEEE" align="left"> Auth Principal </td><td bgcolor="#EEEEEE" align="left"> %A </td><td bgcolor="#EEEEEE" align="left"> calculated </td></tr>
 * <tr><td bgcolor="#EEEEEE" align="left"> % </td><td bgcolor="#EEEEEE" align="left"> %% </td><td bgcolor="#EEEEEE" align="left"> NA </td></tr>
 *
 * </table>
 *
 * <AUTHOR>
 *
 */
public class HttpLogFilter implements Filter {

    private static final Logger log = LoggerFactory.getLogger("httpLog");

    private final String format = "%M %U %Q %C %I %A %R %B";

    @Override
    public void destroy() {
        // Nothing to destroy
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) request;

        MDC.put("username", getUsername());

        if (log.isInfoEnabled()){
            log.info(formatRequest(httpRequest));
        }
        try {
            chain.doFilter(request, response);
        } finally {
            MDC.remove("username");
        }

    }

    @Override
    public void init(FilterConfig config) throws ServletException {
        // nothing to init
    }

    private String formatRequest(HttpServletRequest request) {

        StringBuffer sb = new StringBuffer();
        char[] formatArray = format.toCharArray();
        for (int i = 0; i < formatArray.length; i++) {
            if (formatArray[i] != '%') {
                sb.append(formatArray[i]);

            } else {
                i++;

                switch (formatArray[i]) {

                case 'R':
                    sb.append(request.getRemoteAddr());
                    break;
                case 'M':
                    sb.append(request.getMethod());
                    break;
                case 'C':
                    sb.append(request.getScheme());
                    break;
                case 'S':
                    sb.append(request.getServerName());
                    break;
                case 'P':
                    sb.append(request.getServerPort());
                    break;
                case 'U':
                    sb.append(request.getRequestURI());
                    break;
                case 'L':
                    sb.append(request.getContentLength());
                    break;
                case 'Q':
                    sb.append(request.getQueryString());
                    break;
                case 'I':
                    HttpSession s = request.getSession(false);
                    sb.append(s==null ? "No session": s.getId());
                    break;
                case 'A':
                    sb.append(getUsername());
                    break;
                case 'B':
                    sb.append(request.getHeader("User-Agent"));
                    break;
                case '%':
                    sb.append('%');
                    break;
                default:
                    sb.append('%' + formatArray[i]);
                break;
                }
            }
        }
        return new String(sb);

    }

    private String getUsername() {
        return SecurityUtil.authenticatedUserExists() ? SecurityUtil.getAuthenticatedUsername() : "";
    }

}
