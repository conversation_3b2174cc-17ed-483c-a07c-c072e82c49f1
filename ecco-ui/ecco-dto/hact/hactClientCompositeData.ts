import {HactOutcomeMappingDto} from "../hact-dto";
import {SupportWork} from "../evidence-dto";
import {HactSessionData} from "../session-data/feature-config-domain";
import {
    convertActionHistoryToIntermediateData,
    convertQuestionnaireHistoryToIntermediateData,
    HactAnalysisFromHactAnswerHistoryAccumulator,
    IntermediateDataStructureFromActionHistory,
    IntermediateDataStructureFromQuestionHistory,
    HactAnswerHistorySummary
} from "./hactNotificationHandlerData";
import Sequence = LazyJS.Sequence;
import {QuestionnaireWorkDto} from "../evidence/questionnaire-dto";
import Lazy = require("lazy");

/**
 * The class which takes only the relevant data and builds all necessary structures from it.
 * So, notifications, management and social value reports.
 * NB See HactClientCompositeDataSnapshot below for building this class quickly from persisted data.
 */
export class HactClientCompositeData {
    constructor(
        public hactSessionData: HactSessionData,
        public clientId: number,
        public hactAnswerHistorySummary: HactAnswerHistorySummary,
        public triggerPreSurveyActionHistory: Sequence<IntermediateDataStructureFromActionHistory>,
        public triggerPreSurveyQuestionHistory: Sequence<IntermediateDataStructureFromQuestionHistory>
    ) {}

    /**
     * Clarify that the table relates to survey mappings only for now
     */
    public getSurveyMappings(): HactOutcomeMappingDto[] {
        return this.hactSessionData.triggerMappings;
    }

    public hactQuestionDefIdsFromActionDefIds(actionDefIds: number[]): number[] {
        // get the outcomes for the actions
        const hactOutcomeDefCodes = this.getSurveyMappings()
            .filter(mapping => actionDefIds.indexOf(mapping.actionDefId!) > -1)
            .map(mapping => mapping.hactOutcomeDefCode);
        return this.hactQuestionDefIdsFromHactOutcomeDefCodes(hactOutcomeDefCodes);
    }

    public hactQuestionDefIdsFromQuestionDefIds(questionDefIds: number[]): number[] {
        // get the outcomes for the actions
        const hactOutcomeDefCodes = this.getSurveyMappings()
            .filter(mapping => questionDefIds.indexOf(mapping.questionDefId!) > -1)
            .map(mapping => mapping.hactOutcomeDefCode);
        return this.hactQuestionDefIdsFromHactOutcomeDefCodes(hactOutcomeDefCodes);
    }

    private hactQuestionDefIdsFromHactOutcomeDefCodes(hactOutcomeDefCodes: string[]): number[] {
        // get the questionIds from the outcomes chosen
        const hactQuestionDefIds: number[] = [];
        this.hactSessionData.evidenceSurveys.forEach(evidence => {
            if (hactOutcomeDefCodes.indexOf(evidence.hactOutcomeDefCode) > -1) {
                hactQuestionDefIds.push(evidence.questionDefId);
            }
        });

        return hactQuestionDefIds;
    }

    public actionDefIdsFromHactQuestionDefIds(hactQuestionDefIds: number[]): number[] {
        const hactOutcomeDefCodes =
            this.hactQuestionDefCodesFromHactOutcomeDefIds(hactQuestionDefIds);

        // get the actionDefIds from the outcomes chosen
        const actionDefIds: number[] = [];
        this.getSurveyMappings().forEach(mapping => {
            if (hactOutcomeDefCodes.indexOf(mapping.hactOutcomeDefCode) > -1) {
                actionDefIds.push(mapping.actionDefId!);
            }
        });

        return actionDefIds;
    }

    public questionDefIdsFromHactQuestionDefIds(hactQuestionDefIds: number[]): number[] {
        const hactOutcomeDefCodes =
            this.hactQuestionDefCodesFromHactOutcomeDefIds(hactQuestionDefIds);

        // get the questionDefIds from the outcomes chosen
        const questionDefIds: number[] = [];
        this.getSurveyMappings().forEach(mapping => {
            if (hactOutcomeDefCodes.indexOf(mapping.hactOutcomeDefCode) > -1) {
                questionDefIds.push(mapping.questionDefId!);
            }
        });

        return questionDefIds;
    }

    private hactQuestionDefCodesFromHactOutcomeDefIds(hactQuestionDefIds: number[]): string[] {
        const hactOutcomeDefCodes = this.hactSessionData.evidenceSurveys
            .filter(evidence => hactQuestionDefIds.indexOf(evidence.questionDefId) > -1)
            .map(evidence => evidence.hactOutcomeDefCode);

        return hactOutcomeDefCodes;
    }
}

// TODO A class which takes the intermediate data from a summary table, therefore bypassing vast data loading
// We avoid the need for this for the moment and see if DEV-1539 is sufficient (it limits loading to just HACT triggered support work).
// A hactsocialvaluesnapshot table may look like:
//      c-id, questionDefId, valuableAnswer, year (ie first, second time a valuable answer is recorded), workDate.
//      This could be maintained when a questionnaire is answered, but ideally also needs to intercept deleting or editing questionnaires (though this won't be relevant for previous reporting periods since the values will have been published).
// export class HactClientCompositeDataSnapshot extends HactClientCompositeData {
//      snapshot table to be raw data and avoid processing logic which may change:
//          answers: IntermediateDataStructureFromHactAnswerWork then pass this into accumulateAnswerToSummary for processing logic
//          actions: IntermediateDataStructureFromActionHistory (ignoring daysUntilExpire)

// let hactAnswerHistorySummary = ... we replace 'buildAnswerDataStructure' in the accumulator
// let preSurveyActionHistory = ... load directly from snapshot table
// let preSurveyQuestionHistory = ... load directly from snapshot table
// super(...data...);
// }

/**
 * A class which takes all the data of the client - support work (for triggers) and hact questionnaire answers.
 * Then do the translation to intermediate states which are utilised by other areas - notifications, reports etc.
 */
export class HactClientCompositeDataFull extends HactClientCompositeData {
    // everything loaded from the control
    constructor(
        hactSessionData: HactSessionData,
        clientId: number,
        hactAnswerHistory: QuestionnaireWorkDto[],
        actionTriggerHistory: SupportWork[],
        questionTriggerHistory: QuestionnaireWorkDto[]
    ) {
        // convert the raw data - questionnaire work
        let answerSummaryData = new HactAnalysisFromHactAnswerHistoryAccumulator(
            clientId.toString()
        ).reduce(Lazy(hactAnswerHistory));
        // convert the raw data - support plan work
        let preSurveyActionHistory = convertActionHistoryToIntermediateData(
            Lazy(actionTriggerHistory)
        );
        // TODO be good to filter by those question triggers that are meaningful to hact
        let preSurveyQuestionHistory = convertQuestionnaireHistoryToIntermediateData(
            Lazy(questionTriggerHistory)
        );

        super(
            hactSessionData,
            clientId,
            answerSummaryData,
            preSurveyActionHistory,
            preSurveyQuestionHistory
        );
    }
}
