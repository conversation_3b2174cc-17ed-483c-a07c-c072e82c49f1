<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<%@ include file="/WEB-INF/views/includes.jsp" %>
<%@taglib prefix="page" tagdir="/WEB-INF/tags/page"%>

<link rel="stylesheet" href="${cssBase}/main-styles.css">

<style type="text/css">
    /* tooltip */
    #tooltip {
        position: absolute;
        z-index: 3000;
        border: 1px solid #111;
        background-color: lightyellow;
        padding: 5px;
    }
    #tooltip h3, #tooltip div {
        margin: 0;
    }

    .float {
        width: 32%;
        float: left;
    }
    .notFirst {
        margin-left: 1px;
    }
    .floatContent {
        margin: 0 auto; width: 95%;
    }
    .fixFloatHeight {
        height: 150px; /* min height in pixels */
        float: right;
        width: 1px;
    }
    .fixFloatHeightClear {
        clear: both;
        height: 1px;
        overflow: hidden;
    }

</style>

<script type="text/javascript" src="${scriptsBase}/jquery-ui.js"></script>

<script type="text/javascript">
    (function ($) {
        var examplesOfUse = new Array();
        var videoTimer = null;
        examplesOfUse[0] = "Internet based - available anywhere, anytime";
        examplesOfUse[1] = "Accessible to clients, staff and commissioners";
        examplesOfUse[2] = "Customer Licence linked to contract duration and value";
        examplesOfUse[3] = "Full audit trail";
        examplesOfUse[4] = "Full calendar system";
        examplesOfUse[5] = "Sophisticated sms system";
        examplesOfUse[6] = "Address-lookup feature avoids typing";
        examplesOfUse[7] = "No expensive computer hardware required";
        examplesOfUse[8] = "All backups handled by ecco";
        examplesOfUse[9] = "Data securely held - complies with Data Protection Act";
        var index = Math.floor(Math.random()*examplesOfUse.length); // random generates 0<1

        function next() {
            $('#examplesOfUseNext').blur();
            $('#examplesOfUse').fadeOut('slow', function() {
                $('#examplesOfUseText').text(examplesOfUse[index]);
                index++;
                if (index >= examplesOfUse.length)
                    index = 0;
                $('#examplesOfUse').show();
                $('#examplesOfUse').fadeIn();
            });
        }
        function nextTrigger() {
            next();
            if (videoTimer != null)
                clearTimeout(videoTimer);
            videoTimer = setTimeout(nextTrigger, 5500);
        }
        $(document).ready(function() {
            $('#examplesOfUseNext').click(function() {
                $(this).effect("highlight", {color: "#3c83ca"}, 1000);
                nextTrigger();
                return false;
            });
            nextTrigger();
            $("#examplesOfUseStop").click(function() {
                $(this).effect("highlight", {color: "#3c83ca"}, 1000);
                clearTimeout(videoTimer);
            });
        });
    }(jQuery));
</script>
