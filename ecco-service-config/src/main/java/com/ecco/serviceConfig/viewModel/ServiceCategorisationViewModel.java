package com.ecco.serviceConfig.viewModel;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * No project or service-specific data is provided here, just the names that relate against a given
 * ServiceCategorisation which can then be used in specifying a serviceAllocation, for example.
 */
@Getter
@Setter
@NoArgsConstructor
public class ServiceCategorisationViewModel {

    /** Ignored when POSTing a new entity */
    public Integer id;

    public Boolean disabled;

    public String serviceName;

    public int serviceId;

    public int serviceTypeId;

    public String projectName;

    public Integer projectId;

    public String serviceGroupName;

    public String regionName;

    public Integer regionId;

    public Integer clientGroupId;

    public String clientGroupName;

    public Integer companyId;

    public String companyName;

    public Integer serviceGroupId;

    public Integer buildingId;
}
