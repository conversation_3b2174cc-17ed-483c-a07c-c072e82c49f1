import $ = require("jquery");
import BaseAsyncCommandForm = require("../cmd-queue/BaseAsyncCommandForm");

import BaseAsyncDataControl = require("../controls/BaseAsyncDataControl");
import SelectList = require("../controls/SelectList");
import Building = buildingDtos.Building;
import {EccoDate} from "@eccosolutions/ecco-common";
import {apiClient} from "ecco-components";
import {BuildingAjaxRepository, WorkersAjaxRepository} from "ecco-dto";
import * as buildingDtos from "ecco-dto/building-dto";
import {StaffDto} from "ecco-dto/hr-dto";
import {showModalWithSubmitCancel} from "../components/MUIConverterUtils";
import {StaffPrimaryLocationCommand} from "ecco-commands";

const workerRepository = new WorkersAjaxRepository(apiClient);
const buildingRepository = new BuildingAjaxRepository(apiClient);

class EditLocationForm extends BaseAsyncCommandForm<Building[]> {

    public static showInModal(serviceRecipientId: number, dto: StaffDto, onFinished?: (bldId: number) => void) {
        const form = new EditLocationForm(serviceRecipientId, dto);
        form.setOnFinished( () => {
            onFinished(form.getBuildingId());
        });
        form.load();
        showModalWithSubmitCancel("edit location", "save", "", form.element(),
            () => {form.submitForm()}, "modal-sm");
    }

    private buildingList = new SelectList("building-selection");

    constructor(private srId: number, private dto: StaffDto) {
        super("edit location");
//        this.element()
//            .addClass("col-xs-offset-5 col-xs-7");
        this.buildingList.element().addClass("form-control");
    }

    fetchViewData(): Promise<Building[]> {
        return buildingRepository.findAllBuildings();
    }

    render(buildings: Building[]) {
        this.element().empty()
            .append(this.buildingList.element());
        this.buildingList
                .clear()
                .populateFromList(
                    buildings,
                    (item) => ({
                        key: item.buildingId.toString(),
                        value: item.name}),
                        (item) => item.buildingId == this.dto.primaryLocationId
                );
        this.enableSubmit(); // TODO: poss want to link to onChange of SelectList
    }

    public getBuildingId(): number {
        return this.buildingList.selected(true)
            && parseInt(this.buildingList.selected(true));
    }

    protected override submitForm(): Promise<void> {

        // if there is a change
        if (this.dto.primaryLocationId != this.getBuildingId()) {

            // Send a command with startDate indicating the date the change happens
            const cmd = new StaffPrimaryLocationCommand(this.srId, "staffLocation")
                .changeLocation(this.dto.primaryLocationId, this.getBuildingId())
                .changeStartDate(null, EccoDate.todayLocalTime());

            if (cmd.hasChanges()) {
                this.commandQueue.addCommand(cmd);
            }
        }
        return super.submitForm();
    }
}


/**
 * Visual representation of a container to allow the days a client is attending to be
 * shown and updated (possibly with a comment).
 */
class StaffLocationControl extends BaseAsyncDataControl<StaffDto> {

    /**
     * editable - determines if we let the user edit this data, or read only
     */
    constructor(private srId: number, private workerId: number, private editable: boolean) {
        super();
    }

    protected fetchViewData(): Promise<StaffDto> {
        return workerRepository.findOneWorker(this.workerId);
    }

    /** Render must replace contents of this.element() */
    protected render(dto: StaffDto): void {
        var $span = $("<span>").addClass("button");
        this.element().empty().append($span);

        $span.append(dto.primaryLocationName || "- none selected -");
        // put .click on the span, not on .element() as we don't replace element()
        const onFinished = (bldgId: number) => {
            dto.primaryLocationId = bldgId;
            this.load(); // TODO: should just be render(dto) but we'd need to set the residenceName
        };
        $span.click( () => EditLocationForm.showInModal(this.srId, dto, onFinished) );
    }

}
export = StaffLocationControl;
