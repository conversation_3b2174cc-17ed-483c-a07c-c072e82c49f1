[mysqld]

# from www2
#collation-server = utf8_general_ci
#default-time-zone='+00:00'

# allows only 16MB uploads because the jdbc hex encoding doubles the bytes in transit (see DEV-529)
# this was 20, but there was also the default still enabled below at 16 - now commented
#max_allowed_packet=32M

# FOR CLOUD REPLICATION 17-Nov-22
lower_case_table_names=1
# END from www2

#======= ECCO CUSTOM ========
innodb_buffer_pool_size         = 128M # 512M on www2
