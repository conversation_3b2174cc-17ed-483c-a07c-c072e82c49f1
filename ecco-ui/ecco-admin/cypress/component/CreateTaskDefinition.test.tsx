import {mount} from "@cypress/react";
import * as React from "react";
import {
    LayoutTaskDefinitionWrapper,
    TaskDefinitionCreate
} from "../../service-config/TaskDefinitionCreate";
import {EccoAPI} from "ecco-components/EccoAPI";
import {TestServicesContextProvider} from "ecco-components/test-support/TestServicesContextProvider";
import {CommandFormTest, CommandFormTestOutput} from "ecco-components/cmd-queue/testUtils";
import {CommandForm} from "ecco-components";
import {Command} from "ecco-commands";

describe("render CreateTaskDefinition layout", () => {
    it("layout", () => {
        mount(<LayoutTaskDefinitionWrapper />);

        cy.get('[name*="taskName"]').type("my new task...");
        cy.get('[name*="taskType"]').parent().click().get("li").eq(1).click();
        cy.get('[name*="taskType"]').invoke("val").should("eq", "EVIDENCE_SUPPORT");
    });
});

const overrides = {} as EccoAPI;

describe("full CreateTaskDefinition", () => {
    it("layout", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommandFormTest>
                    {(form: CommandForm, cmdEmitted: Command[]) => (
                        <>
                            <TaskDefinitionCreate
                                init={{
                                    taskDefinitionId: undefined,
                                    taskName: undefined,
                                    taskType: undefined
                                }}
                            />
                            <CommandFormTestOutput cmdEmitted={cmdEmitted} />
                        </>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );

        cy.get('[name*="taskName"]').type("my new task...");
        cy.get('[name*="taskType"]').parent().click().get("li").eq(1).click();
        cy.get('[name*="taskType"]').invoke("val").should("eq", "EVIDENCE_SUPPORT");
        cy.findByRole("button", {name: "submit"}).click();

        // TEST
        cy.contains('"nameChange":{"from":null,"to":"my new task..."},"type":"EVIDENCE_SUPPORT"}');
    });
});
